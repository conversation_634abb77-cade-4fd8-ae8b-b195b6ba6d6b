import 'package:user_app/constants/vote_type.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/comment/comment_vo.dart';

class CommentApi extends BaseApi {
  static const String commentPath = '/comments';

  CommentApi(super.dio);

  Future<List<CommentVo>> fetchComments(int momentId) async {
    final response = await safeApiCall(
      () => dio.get('$commentPath/list/$momentId'),
      (data) {
        // 后端返回的是分页数据，需要提取records字段
        if (data is Map<String, dynamic> && data['records'] is List) {
          final records = data['records'] as List;
          return records.map((item) => CommentVo.fromMap(item)).toList();
        }
        return <CommentVo>[];
      },
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? [];
  }

  Future<void> addComment(int id, String content) async {
    final response = await safeApiCall(
      () => dio.post('$commentPath/create', data: {
        'moment_id': id,
        'content': content,
      }),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  Future<void> voteComment(num commentId, VoteType voteType) async {
    final response = await safeApiCall(
      () => dio.get('$commentPath/$commentId/vote/${voteType.value}'),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }

  Future<void> replyToComment(num momentId, num commentId, String text) async {
    final response = await safeApiCall(
      () => dio.post('$commentPath/create', data: {
        'moment_id': momentId,
        'parent_id': commentId,
        'content': text,
      }),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }
  }
}
