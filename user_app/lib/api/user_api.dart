import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/moment/user_fans_attentions_response.dart';
import 'package:user_app/models/user.dart';

class UserApi extends BaseApi {
  UserApi(super.dio);

  Future<User> getCurrentUser() async {
    final response = await safeApiCall(
      () => dio.get('/users'),
      (data) => User.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "获取当前用户信息失败");
    }

    return response.data!;
  }

  Future<User> getUserProfile(num userId) async {
    final response = await safeApiCall(
      () => dio.get('/user-profiles/$userId'),
      (data) => User.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "获取用户信息失败");
    }

    return response.data!;
  }

  Future<UserFansAttentionsResponse> getFans(
      UserFansAttentionsRequest request) async {
    final response = await safeApiCall(
      () => dio.post('/user-follows/list/follows', data: request.toJson()),
      (data) => UserFansAttentionsResponse.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "获取粉丝列表失败");
    }

    return response.data!;
  }

  Future<UserFansAttentionsResponse> getAttentions(
      UserFansAttentionsRequest request) async {
    final response = await safeApiCall(
      () => dio.post('/user-follows/list/attentions', data: request.toJson()),
      (data) => UserFansAttentionsResponse.fromMap(data),
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    if (response.data == null) {
      throw ApiError(code: 404, message: "获取关注列表失败");
    }

    return response.data!;
  }

  Future<List<User>> searchUsers(String query, int page, int pageSize) async {
    final response = await safeApiCall(
      () => dio.get('/users/search', queryParameters: {
        'query': query,
        'page': page,
        'size': pageSize,
      }),
      (data) {
        if (data is List) {
          return data.map((item) => User.fromMap(item)).toList();
        }
        return <User>[];
      },
    );

    if (!response.isSuccess) {
      throw ApiError(code: response.code, message: response.message);
    }

    return response.data ?? [];
  }
}
