// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'comment_vo.dart';

class CommentVoMapper extends ClassMapperBase<CommentVo> {
  CommentVoMapper._();

  static CommentVoMapper? _instance;
  static CommentVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CommentVoMapper._());
      CommentVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'CommentVo';

  static int _$id(CommentVo v) => v.id;
  static const Field<CommentVo, int> _f$id = Field('id', _$id);
  static int _$momentId(CommentVo v) => v.momentId;
  static const Field<CommentVo, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id');
  static int _$userId(CommentVo v) => v.userId;
  static const Field<CommentVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static int? _$parentCommentId(CommentVo v) => v.parentCommentId;
  static const Field<CommentVo, int> _f$parentCommentId = Field(
      'parentCommentId', _$parentCommentId,
      key: r'parent_comment_id', opt: true);
  static String _$userName(CommentVo v) => v.userName;
  static const Field<CommentVo, String> _f$userName =
      Field('userName', _$userName, key: r'user_name');
  static String? _$userAvatarUrl(CommentVo v) => v.userAvatarUrl;
  static const Field<CommentVo, String> _f$userAvatarUrl = Field(
      'userAvatarUrl', _$userAvatarUrl,
      key: r'user_avatar_url', opt: true);
  static String _$content(CommentVo v) => v.content;
  static const Field<CommentVo, String> _f$content =
      Field('content', _$content);
  static DateTime _$createdAt(CommentVo v) => v.createdAt;
  static const Field<CommentVo, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at');
  static num _$upVotes(CommentVo v) => v.upVotes;
  static const Field<CommentVo, num> _f$upVotes =
      Field('upVotes', _$upVotes, key: r'up_votes');
  static num _$downVotes(CommentVo v) => v.downVotes;
  static const Field<CommentVo, num> _f$downVotes =
      Field('downVotes', _$downVotes, key: r'down_votes');
  static bool? _$upVoted(CommentVo v) => v.upVoted;
  static const Field<CommentVo, bool> _f$upVoted =
      Field('upVoted', _$upVoted, key: r'up_voted', opt: true);
  static bool? _$downVoted(CommentVo v) => v.downVoted;
  static const Field<CommentVo, bool> _f$downVoted =
      Field('downVoted', _$downVoted, key: r'down_voted', opt: true);
  static String? _$updateTime(CommentVo v) => v.updateTime;
  static const Field<CommentVo, String> _f$updateTime =
      Field('updateTime', _$updateTime, key: r'update_time', opt: true);
  static List<CommentVo> _$subComments(CommentVo v) => v.subComments;
  static const Field<CommentVo, List<CommentVo>> _f$subComments =
      Field('subComments', _$subComments, key: r'sub_comments');

  @override
  final MappableFields<CommentVo> fields = const {
    #id: _f$id,
    #momentId: _f$momentId,
    #userId: _f$userId,
    #parentCommentId: _f$parentCommentId,
    #userName: _f$userName,
    #userAvatarUrl: _f$userAvatarUrl,
    #content: _f$content,
    #createdAt: _f$createdAt,
    #upVotes: _f$upVotes,
    #downVotes: _f$downVotes,
    #upVoted: _f$upVoted,
    #downVoted: _f$downVoted,
    #updateTime: _f$updateTime,
    #subComments: _f$subComments,
  };

  static CommentVo _instantiate(DecodingData data) {
    return CommentVo(
        id: data.dec(_f$id),
        momentId: data.dec(_f$momentId),
        userId: data.dec(_f$userId),
        parentCommentId: data.dec(_f$parentCommentId),
        userName: data.dec(_f$userName),
        userAvatarUrl: data.dec(_f$userAvatarUrl),
        content: data.dec(_f$content),
        createdAt: data.dec(_f$createdAt),
        upVotes: data.dec(_f$upVotes),
        downVotes: data.dec(_f$downVotes),
        upVoted: data.dec(_f$upVoted),
        downVoted: data.dec(_f$downVoted),
        updateTime: data.dec(_f$updateTime),
        subComments: data.dec(_f$subComments));
  }

  @override
  final Function instantiate = _instantiate;

  static CommentVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<CommentVo>(map);
  }

  static CommentVo fromJson(String json) {
    return ensureInitialized().decodeJson<CommentVo>(json);
  }
}

mixin CommentVoMappable {
  String toJson() {
    return CommentVoMapper.ensureInitialized()
        .encodeJson<CommentVo>(this as CommentVo);
  }

  Map<String, dynamic> toMap() {
    return CommentVoMapper.ensureInitialized()
        .encodeMap<CommentVo>(this as CommentVo);
  }

  CommentVoCopyWith<CommentVo, CommentVo, CommentVo> get copyWith =>
      _CommentVoCopyWithImpl<CommentVo, CommentVo>(
          this as CommentVo, $identity, $identity);
  @override
  String toString() {
    return CommentVoMapper.ensureInitialized()
        .stringifyValue(this as CommentVo);
  }

  @override
  bool operator ==(Object other) {
    return CommentVoMapper.ensureInitialized()
        .equalsValue(this as CommentVo, other);
  }

  @override
  int get hashCode {
    return CommentVoMapper.ensureInitialized().hashValue(this as CommentVo);
  }
}

extension CommentVoValueCopy<$R, $Out> on ObjectCopyWith<$R, CommentVo, $Out> {
  CommentVoCopyWith<$R, CommentVo, $Out> get $asCommentVo =>
      $base.as((v, t, t2) => _CommentVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CommentVoCopyWith<$R, $In extends CommentVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, CommentVo, CommentVoCopyWith<$R, CommentVo, CommentVo>>
      get subComments;
  $R call(
      {int? id,
      int? momentId,
      int? userId,
      int? parentCommentId,
      String? userName,
      String? userAvatarUrl,
      String? content,
      DateTime? createdAt,
      num? upVotes,
      num? downVotes,
      bool? upVoted,
      bool? downVoted,
      String? updateTime,
      List<CommentVo>? subComments});
  CommentVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _CommentVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, CommentVo, $Out>
    implements CommentVoCopyWith<$R, CommentVo, $Out> {
  _CommentVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<CommentVo> $mapper =
      CommentVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, CommentVo, CommentVoCopyWith<$R, CommentVo, CommentVo>>
      get subComments => ListCopyWith($value.subComments,
          (v, t) => v.copyWith.$chain(t), (v) => call(subComments: v));
  @override
  $R call(
          {int? id,
          int? momentId,
          int? userId,
          Object? parentCommentId = $none,
          String? userName,
          Object? userAvatarUrl = $none,
          String? content,
          DateTime? createdAt,
          num? upVotes,
          num? downVotes,
          Object? upVoted = $none,
          Object? downVoted = $none,
          Object? updateTime = $none,
          List<CommentVo>? subComments}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (momentId != null) #momentId: momentId,
        if (userId != null) #userId: userId,
        if (parentCommentId != $none) #parentCommentId: parentCommentId,
        if (userName != null) #userName: userName,
        if (userAvatarUrl != $none) #userAvatarUrl: userAvatarUrl,
        if (content != null) #content: content,
        if (createdAt != null) #createdAt: createdAt,
        if (upVotes != null) #upVotes: upVotes,
        if (downVotes != null) #downVotes: downVotes,
        if (upVoted != $none) #upVoted: upVoted,
        if (downVoted != $none) #downVoted: downVoted,
        if (updateTime != $none) #updateTime: updateTime,
        if (subComments != null) #subComments: subComments
      }));
  @override
  CommentVo $make(CopyWithData data) => CommentVo(
      id: data.get(#id, or: $value.id),
      momentId: data.get(#momentId, or: $value.momentId),
      userId: data.get(#userId, or: $value.userId),
      parentCommentId: data.get(#parentCommentId, or: $value.parentCommentId),
      userName: data.get(#userName, or: $value.userName),
      userAvatarUrl: data.get(#userAvatarUrl, or: $value.userAvatarUrl),
      content: data.get(#content, or: $value.content),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      upVotes: data.get(#upVotes, or: $value.upVotes),
      downVotes: data.get(#downVotes, or: $value.downVotes),
      upVoted: data.get(#upVoted, or: $value.upVoted),
      downVoted: data.get(#downVoted, or: $value.downVoted),
      updateTime: data.get(#updateTime, or: $value.updateTime),
      subComments: data.get(#subComments, or: $value.subComments));

  @override
  CommentVoCopyWith<$R2, CommentVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CommentVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
