import 'package:dart_mappable/dart_mappable.dart';

part 'comment_vo.mapper.dart';

@MappableClass()
class CommentVo with CommentVoMappable {
  final int id;
  final int momentId;
  final int userId;
  @MappableField(key: 'parentId')
  final int? parentCommentId;
  final String userName;
  @MappableField(key: 'userAvatar')
  final String? userAvatarUrl;
  final String content;
  num upVotes;
  num downVotes;
  bool? upVoted = false;
  bool? downVoted = false;
  @MappableField(key: 'createdAt')
  final DateTime createTime;
  final String? updateTime;
  @MappableField(key: 'replies')
  List<CommentVo> subComments = [];

  CommentVo({
    required this.id,
    required this.momentId,
    required this.userId,
    this.parentCommentId,
    required this.userName,
    this.userAvatarUrl,
    required this.content,
    required this.createTime,
    required this.upVotes,
    required this.downVotes,
    this.upVoted,
    this.downVoted,
    this.updateTime,
    required this.subComments,
  });

  static final fromMap = CommentVoMapper.fromMap;

  static CommentVo? findById(List<CommentVo> comments, num targetId) {
    for (var comment in comments) {
      if (comment.id == targetId) {
        return comment;
      }
      if (comment.subComments.isNotEmpty) {
        final foundInSub = findById(comment.subComments, targetId);
        if (foundInSub != null) {
          return foundInSub;
        }
      }
    }
    return null;
  }
}
