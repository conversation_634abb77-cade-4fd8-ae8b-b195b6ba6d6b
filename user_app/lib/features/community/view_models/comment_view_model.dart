import 'package:flutter/foundation.dart';
import 'package:user_app/models/comment/comment_vo.dart';
import 'package:user_app/services/comment_service.dart';
import 'package:user_app/constants/vote_type.dart';

class CommentViewModel extends ChangeNotifier {
  final CommentService _commentService;
  
  CommentViewModel(this._commentService);

  // 评论数据，按动态ID分组
  final Map<int, List<CommentVo>> _commentsByMoment = {};
  bool _isLoading = false;

  // Getters
  bool get isLoading => _isLoading;
  
  List<CommentVo> getCommentsForMoment(int momentId) {
    return _commentsByMoment[momentId] ?? [];
  }

  // 加载评论
  Future<void> loadComments(int momentId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final comments = await _commentService.fetchComments(momentId);
      _commentsByMoment[momentId] = comments;
    } catch (e) {
      debugPrint('Load comments error: $e');
      _commentsByMoment[momentId] = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 添加评论
  Future<void> addComment(int momentId, String content) async {
    try {
      await _commentService.addComment(momentId, content);
      // 重新加载评论列表
      await loadComments(momentId);
    } catch (e) {
      debugPrint('Add comment error: $e');
      rethrow;
    }
  }

  // 回复评论
  Future<void> replyToComment(int momentId, int commentId, String content) async {
    try {
      await _commentService.replyToComment(momentId, commentId, content);
      // 重新加载评论列表
      await loadComments(momentId);
    } catch (e) {
      debugPrint('Reply to comment error: $e');
      rethrow;
    }
  }

  // 评论点赞/踩
  Future<void> voteComment(int commentId, bool isUpvote) async {
    try {
      final voteType = isUpvote ? VoteType.upVote : VoteType.downVote;
      await _commentService.voteComment(commentId, voteType);
      
      // 更新本地状态
      _updateCommentVoteStatus(commentId, isUpvote);
      notifyListeners();
    } catch (e) {
      debugPrint('Vote comment error: $e');
      rethrow;
    }
  }

  // 更新评论的投票状态
  void _updateCommentVoteStatus(int commentId, bool isUpvote) {
    for (final comments in _commentsByMoment.values) {
      for (final comment in comments) {
        if (comment.id == commentId) {
          if (isUpvote) {
            // 点赞
            if (comment.upVoted == true) {
              // 取消点赞
              comment.upVoted = false;
              comment.upVotes = comment.upVotes - 1;
            } else {
              // 点赞
              comment.upVoted = true;
              comment.upVotes = comment.upVotes + 1;
              // 如果之前踩过，取消踩
              if (comment.downVoted == true) {
                comment.downVoted = false;
                comment.downVotes = comment.downVotes - 1;
              }
            }
          } else {
            // 踩
            if (comment.downVoted == true) {
              // 取消踩
              comment.downVoted = false;
              comment.downVotes = comment.downVotes - 1;
            } else {
              // 踩
              comment.downVoted = true;
              comment.downVotes = comment.downVotes + 1;
              // 如果之前点赞过，取消点赞
              if (comment.upVoted == true) {
                comment.upVoted = false;
                comment.upVotes = comment.upVotes - 1;
              }
            }
          }
          return;
        }
        
        // 检查子评论
        for (final subComment in comment.subComments) {
          if (subComment.id == commentId) {
            if (isUpvote) {
              if (subComment.upVoted == true) {
                subComment.upVoted = false;
                subComment.upVotes = subComment.upVotes - 1;
              } else {
                subComment.upVoted = true;
                subComment.upVotes = subComment.upVotes + 1;
                if (subComment.downVoted == true) {
                  subComment.downVoted = false;
                  subComment.downVotes = subComment.downVotes - 1;
                }
              }
            } else {
              if (subComment.downVoted == true) {
                subComment.downVoted = false;
                subComment.downVotes = subComment.downVotes - 1;
              } else {
                subComment.downVoted = true;
                subComment.downVotes = subComment.downVotes + 1;
                if (subComment.upVoted == true) {
                  subComment.upVoted = false;
                  subComment.upVotes = subComment.upVotes - 1;
                }
              }
            }
            return;
          }
        }
      }
    }
  }

  // 清除特定动态的评论
  void clearCommentsForMoment(int momentId) {
    _commentsByMoment.remove(momentId);
    notifyListeners();
  }

  // 清除所有评论
  void clearAllComments() {
    _commentsByMoment.clear();
    notifyListeners();
  }
}
