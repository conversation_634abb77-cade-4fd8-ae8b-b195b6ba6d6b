import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/widgets/comment_section.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class MomentDetailPage extends StatefulWidget {
  final int momentId;
  final MomentVo? initialMoment;

  const MomentDetailPage({
    super.key,
    required this.momentId,
    this.initialMoment,
  });

  @override
  State<MomentDetailPage> createState() => _MomentDetailPageState();
}

class _MomentDetailPageState extends State<MomentDetailPage> {
  MomentVo? moment;
  bool isLoading = false;
  String? error;

  // State for the comment input field, moved from CommentSection
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isReplying = false;
  int? _replyToCommentId;
  String? _replyToUserName;

  @override
  void initState() {
    super.initState();
    if (widget.initialMoment != null) {
      moment = widget.initialMoment;
    } else {
      _loadMomentDetail();
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadMomentDetail() async {
    setState(() => isLoading = true);
    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      final momentDetail = await momentService.getById(widget.momentId);
      if (mounted) {
        setState(() {
          moment = momentDetail;
          isLoading = false;
          error = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  // Helper methods for comment input, moved from CommentSection
  bool get _isAuthenticated {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('需要登录'),
        content: const Text('请先登录后再进行评论'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.login);
            },
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  void _startReply(int commentId, String userName) {
    setState(() {
      _isReplying = true;
      _replyToCommentId = commentId;
      _replyToUserName = userName;
    });
    _focusNode.requestFocus();
  }

  void _cancelReply() {
    setState(() {
      _isReplying = false;
      _replyToCommentId = null;
      _replyToUserName = null;
      _commentController.clear();
    });
    _focusNode.unfocus();
  }

  Future<void> _submitComment() async {
    if (!_isAuthenticated) {
      _showLoginDialog();
      return;
    }

    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    final commentViewModel = context.read<CommentViewModel>();
    try {
      if (_isReplying && _replyToCommentId != null) {
        await commentViewModel.replyToComment(
          widget.momentId,
          _replyToCommentId!,
          content,
        );
      } else {
        await commentViewModel.addComment(widget.momentId, content);
      }
      _cancelReply(); // Clears text, resets reply state, and unfocuses
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('评论发布成功'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('评论发布失败: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('动态详情'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      // The comment input field is now in the bottomNavigationBar
      bottomNavigationBar: _buildCommentInput(),
      body: Builder(
        builder: (context) {
          if (isLoading && moment == null) {
            return const Center(child: CircularProgressIndicator());
          }
          if (error != null) {
            return Center(child: Text('加载失败: $error'));
          }
          if (moment == null) {
            return const Center(child: Text('动态不存在'));
          }
          return RefreshIndicator(
            onRefresh: _loadMomentDetail,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              // Padding at the bottom to ensure content isn't hidden by the input bar
              padding: const EdgeInsets.only(bottom: 100),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // User info, content, images, etc.
                        _buildMomentHeader(theme, colorScheme),
                        const SizedBox(height: 16),
                        if (moment!.content != null &&
                            moment!.content!.isNotEmpty)
                          Text(
                            moment!.content!,
                            style: theme.textTheme.bodyLarge
                                ?.copyWith(height: 1.5),
                          ),
                        if (moment!.content != null &&
                            moment!.content!.isNotEmpty)
                          const SizedBox(height: 16),
                        if (moment!.pictures != null &&
                            moment!.pictures!.isNotEmpty)
                          _buildImageGrid(moment!.pictures!),
                        if (moment!.pictures != null &&
                            moment!.pictures!.isNotEmpty)
                          const SizedBox(height: 16),
                        if (moment!.addressDetail != null &&
                            moment!.addressDetail!.isNotEmpty)
                          _buildLocationInfo(theme),
                        if (moment!.addressDetail != null &&
                            moment!.addressDetail!.isNotEmpty)
                          const SizedBox(height: 16),
                        _buildStatsRow(),
                      ],
                    ),
                  ),

                  // Divider
                  Divider(color: Colors.grey.shade200, height: 1, thickness: 8),

                  // The refactored CommentSection widget
                  CommentSection(
                    momentId: moment!.id,
                    onStartReply: _startReply, // Pass the callback
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Widget for the comment input bar at the bottom
  Widget _buildCommentInput() {
    return Container(
      padding: EdgeInsets.fromLTRB(
          16, 12, 16, 12 + MediaQuery.of(context).viewInsets.bottom),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 10)
        ],
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isReplying) ...[
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text('回复 $_replyToUserName',
                        style: TextStyle(
                            fontSize: 13, color: Colors.blue.shade800)),
                    const Spacer(),
                    GestureDetector(
                      onTap: _cancelReply,
                      child: Icon(Icons.close,
                          size: 16, color: Colors.blue.shade800),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      hintText:
                          _isReplying ? '回复 $_replyToUserName...' : '写下你的评论...',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      fillColor: Colors.grey.shade100,
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _submitComment(),
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: _submitComment,
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: Theme.of(context).primaryColor,
                    child:
                        const Icon(Icons.send, color: Colors.white, size: 20),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Extracted UI building methods for clarity
  Widget _buildMomentHeader(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: colorScheme.primaryContainer,
          // TODO: Replace with actual user avatar
          child: Icon(Icons.person,
              size: 20, color: colorScheme.onPrimaryContainer),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                moment!.publisher.name,
                style: theme.textTheme.titleMedium
                    ?.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 2),
              Text(
                _formatTime((moment!.createTime ?? DateTime.now()).toString()),
                style: theme.textTheme.bodySmall
                    ?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(children: [
        Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Expanded(
          child: Text(moment!.addressDetail!,
              style: theme.textTheme.bodySmall
                  ?.copyWith(color: Colors.grey.shade600)),
        ),
      ]),
    );
  }

  Widget _buildStatsRow() {
    return Row(children: [
      _buildStatItem(
          icon: Icons.thumb_up_outlined,
          count: moment!.numberOfLikes,
          label: '点赞'),
      const SizedBox(width: 24),
      _buildStatItem(
          icon: Icons.comment_outlined,
          count: moment!.numberOfComments.toInt(),
          label: '评论'),
    ]);
  }

  // Other helper methods remain the same...
  Widget _buildImageGrid(List<String> images) {
    if (images.isEmpty) return const SizedBox.shrink();
    // ... (rest of the image grid logic is unchanged)
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images.length == 1 ? 1 : (images.length == 2 ? 2 : 3),
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            imageUrl: images[index],
            fit: BoxFit.cover,
            placeholder: (context, url) =>
                Container(color: Colors.grey.shade200),
            errorWidget: (context, url, error) => Container(
                color: Colors.grey.shade200, child: const Icon(Icons.error)),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
      {required IconData icon, required int count, required String label}) {
    // ... (stat item logic is unchanged)
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(count.toString(),
            style: TextStyle(
                color: Colors.grey.shade600, fontWeight: FontWeight.w500)),
        const SizedBox(width: 4),
        Text(label,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
      ],
    );
  }

  String _formatTime(String? timeString) {
    // ... (time formatting logic is unchanged)
    if (timeString == null) return '未知时间';
    try {
      final time = DateTime.parse(timeString);
      final now = DateTime.now();
      final difference = now.difference(time);
      if (difference.inMinutes < 1) return '刚刚';
      if (difference.inHours < 1) return '${difference.inMinutes}分钟前';
      if (difference.inDays < 1) return '${difference.inHours}小时前';
      if (difference.inDays < 7) return '${difference.inDays}天前';
      return '${time.year}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeString;
    }
  }
}
