import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/FishingScaffold.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/screens/login_page.dart';
import 'package:user_app/features/auth/screens/register_page.dart';
import 'package:user_app/features/auth/screens/reset_password_page.dart';
import 'package:user_app/features/community/screens/community_page.dart';
import 'package:user_app/features/fishing_spots/screens/fishing_spots_page.dart';
import 'package:user_app/features/fishing_spots/screens/publish_moment_page.dart';
import 'package:user_app/features/search/screens/search_page.dart';
import 'package:user_app/features/tips/screens/tips_page.dart';
import 'package:user_app/screens/mine/mine_page.dart';
import 'package:user_app/screens/msg/message_page.dart';
import 'package:user_app/screens/other_profile_page.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/attentions_list_page.dart';
import 'package:user_app/widgets/fade_transition_page.dart';
import 'package:user_app/widgets/fans_list_page.dart';
import 'package:user_app/widgets/pictures_page_view.dart';

final rootShellNavigatorKey = GlobalKey<NavigatorState>();
final appShellNavigatorKey = GlobalKey<NavigatorState>();

class AppRouterConfig {
  static final List<String> _authRoutes = [
    AppRoutes.message,
    AppRoutes.mine,
  ];

  static final router = GoRouter(
    initialLocation: AppRoutes.fishingSpots,
    navigatorKey: rootShellNavigatorKey,
    redirect: (BuildContext context, GoRouterState state) {
      final isGoingToAuth = _authRoutes.contains(state.matchedLocation);
      final isAuthed = context.read<AuthViewModel>().isUserLoggedIn();

      if (isGoingToAuth && !isAuthed) {
        return '${AppRoutes.login}?from=${state.fullPath}';
      }

      if (isAuthed && state.matchedLocation == AppRoutes.login) {
        final fromPath = state.uri.queryParameters['from'];
        return fromPath ?? AppRoutes.fishingSpots;
      }

      return null;
    },
    routes: <RouteBase>[
      ShellRoute(
        navigatorKey: appShellNavigatorKey,
        builder: (context, state, child) {
          return FishingScaffold(
            selectedIndex: switch (state.uri.path) {
              var p when p.startsWith(AppRoutes.fishingSpots) => 0,
              var p when p.startsWith(AppRoutes.community) => 1,
              var p when p.startsWith(AppRoutes.tips) => 2,
              var p when p.startsWith(AppRoutes.message) => 3,
              var p when p.startsWith(AppRoutes.mine) => 4,
              _ => 0,
            },
            child: child,
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.fishingSpots,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const FishingSpotsPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.community,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const CommunityPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.tips,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const TipsPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.message,
            pageBuilder:
                _fadeTransitionBuilder((context) => const MessagePage()),
          ),
          GoRoute(
            path: AppRoutes.mine,
            pageBuilder: _fadeTransitionBuilder((context) => const MinePage()),
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) {
          final fromPath = state.uri.queryParameters['from'];
          return Builder(builder: (context) {
            return LoginPage(fromPath: fromPath);
          });
        },
      ),
      GoRoute(
        path: AppRoutes.register,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const RegisterPage(),
        ),
      ),
      GoRoute(
        path: AppRoutes.resetPassword,
        pageBuilder:
            _fadeTransitionBuilder((context) => const ResetPasswordPage()),
      ),
      GoRoute(
        path: '${AppRoutes.profile}/:userId',
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return OtherProfilePage(userId: int.parse(userId));
          });
        },
      ),
      GoRoute(
        path: AppRoutes.fansPage,
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return FansListPage(userId: int.parse(userId));
          });
        },
      ),
      GoRoute(
        path: AppRoutes.attentionsPage,
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return AttentionsListPage(userId: int.parse(userId));
          });
        },
      ),
      GoRoute(
        path: AppRoutes.previewPictures,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const PicturesPageView(),
        ),
      ),
      GoRoute(
        path: AppRoutes.publishMoment,
        builder: (context, state) {
          final momentType = state.extra as String?;
          return Builder(
            builder: (context) {
              return PublishMomentPage(initialMomentType: momentType);
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.search,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const SearchPage(),
        ),
      ),
    ],
  );

  static GoRouterPageBuilder _fadeTransitionBuilder(WidgetBuilder builder) {
    return (context, state) {
      return FadeTransitionPage<dynamic>(
        key: state.pageKey,
        child: Builder(
          builder: builder,
        ),
      );
    };
  }
}
