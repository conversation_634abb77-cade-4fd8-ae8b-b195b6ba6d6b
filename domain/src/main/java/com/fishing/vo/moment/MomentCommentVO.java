package com.fishing.vo.moment;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态评论信息展示对象
 */
@Data
public class MomentCommentVO {
    /**
     * 评论ID
     */
    private Long id;
    
    /**
     * 动态ID
     */
    private Long momentId;
    
    /**
     * 评论用户ID
     */
    private Long userId;
    
    /**
     * 评论用户名称
     */
    private String userName;
    
    /**
     * 评论用户头像
     */
    private String userAvatar;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 父评论ID
     */
    private Long parentId;
    
    /**
     * 回复的评论信息
     */
    private MomentCommentVO parentComment;
    
    /**
     * 子评论列表
     */
    private List<MomentCommentVO> replies;
    
    /**
     * 评论时间
     */
    private LocalDateTime createdAt;

    /**
     * 点赞数
     */
    private Integer upVotes = 0;

    /**
     * 踩数
     */
    private Integer downVotes = 0;

    /**
     * 当前用户是否点赞
     */
    private Boolean upVoted = false;

    /**
     * 当前用户是否踩
     */
    private Boolean downVoted = false;
}