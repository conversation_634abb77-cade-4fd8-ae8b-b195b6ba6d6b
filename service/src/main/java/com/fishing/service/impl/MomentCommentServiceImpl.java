package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.domain.User;
import com.fishing.domain.moment.MomentComment;
import com.fishing.mapper.MomentCommentMapper;
import com.fishing.mapper.UserMapper;
import com.fishing.service.MomentCommentService;
import com.fishing.vo.moment.MomentCommentVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态评论服务实现类
 */
@Service
@RequiredArgsConstructor
public class MomentCommentServiceImpl extends ServiceImpl<MomentCommentMapper, MomentComment> implements MomentCommentService {

    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addComment(MomentComment comment) {
        // 设置评论时间
        comment.setCreatedAt(LocalDateTime.now());
        
        // 保存评论
        save(comment);
        
        return comment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Long commentId, Long userId) {
        // 查询评论信息
        MomentComment comment = getById(commentId);
        if (comment == null) {
            return false;
        }
        
        // 校验权限
        if (!comment.getUserId().equals(userId)) {
            return false;
        }
        
        // 删除评论
        return removeById(commentId);
    }

    @Override
    public Page<MomentCommentVO> getCommentsByMomentId(Long momentId, Page<MomentComment> page) {
        return getCommentsByMomentId(momentId, page, null);
    }

    @Override
    public Page<MomentCommentVO> getCommentsByMomentId(Long momentId, Page<MomentComment> page, Long currentUserId) {
        // 查询所有一级评论（没有父评论ID的评论）
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentComment::getMomentId, momentId)
               .isNull(MomentComment::getParentId)
               .orderByAsc(MomentComment::getCreatedAt);

        Page<MomentComment> commentPage = page(page, wrapper);

        return convertToCommentVOPage(commentPage, currentUserId);
    }

    @Override
    public Page<MomentCommentVO> getRepliesByCommentId(Long commentId, Page<MomentComment> page) {
        return getRepliesByCommentId(commentId, page, null);
    }

    @Override
    public Page<MomentCommentVO> getRepliesByCommentId(Long commentId, Page<MomentComment> page, Long currentUserId) {
        // 查询所有回复评论
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentComment::getParentId, commentId)
               .orderByAsc(MomentComment::getCreatedAt);

        Page<MomentComment> commentPage = page(page, wrapper);

        return convertToCommentVOPage(commentPage, currentUserId);
    }

    @Override
    public Integer getCommentCount(Long momentId) {
        // 查询动态的总评论数
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentComment::getMomentId, momentId);
        
        return Math.toIntExact(count(wrapper));
    }

    @Override
    public List<Long> batchGetCommentCounts(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询所有动态的评论数
        List<Long> result = new ArrayList<>(momentIds.size());
        for (Long momentId : momentIds) {
            LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MomentComment::getMomentId, momentId);
            long commentCount = count(wrapper);
            result.add(commentCount);
        }
        
        return result;
    }
    
    /**
     * 将评论分页对象转换为评论VO分页对象
     */
    private Page<MomentCommentVO> convertToCommentVOPage(Page<MomentComment> page) {
        return convertToCommentVOPage(page, null);
    }

    /**
     * 将评论分页对象转换为评论VO分页对象（支持用户相关信息）
     */
    private Page<MomentCommentVO> convertToCommentVOPage(Page<MomentComment> page, Long currentUserId) {
        Page<MomentCommentVO> resultPage = new Page<>();
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setTotal(page.getTotal());
        
        List<MomentComment> records = page.getRecords();
        if (records.isEmpty()) {
            resultPage.setRecords(new ArrayList<>());
            return resultPage;
        }
        
        // 获取评论的用户ID列表
        List<Long> userIds = records.stream()
                .map(MomentComment::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        // 批量查询用户信息
        List<User> users = userMapper.selectBatchIds(userIds);
        Map<Long, User> userMap = users.stream()
                .collect(Collectors.toMap(User::getId, user -> user));
        
        // 获取评论ID列表，用于查询子评论
        List<Long> commentIds = records.stream()
                .map(MomentComment::getId)
                .collect(Collectors.toList());
        
        // 查询这些评论的子评论
        Map<Long, List<MomentComment>> repliesMap = new HashMap<>();
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentComment::getParentId, commentIds);
        List<MomentComment> replies = list(wrapper);
        
        // 按父评论ID分组
        for (MomentComment reply : replies) {
            Long parentId = reply.getParentId();
            if (!repliesMap.containsKey(parentId)) {
                repliesMap.put(parentId, new ArrayList<>());
            }
            repliesMap.get(parentId).add(reply);
        }
        
        // 转换评论列表
        List<MomentCommentVO> voList = new ArrayList<>();
        for (MomentComment comment : records) {
            MomentCommentVO vo = convertToCommentVO(comment, userMap, repliesMap, currentUserId);
            voList.add(vo);
        }
        
        resultPage.setRecords(voList);
        return resultPage;
    }
    
    /**
     * 将评论对象转换为评论VO对象
     */
    private MomentCommentVO convertToCommentVO(MomentComment comment, Map<Long, User> userMap, Map<Long, List<MomentComment>> repliesMap) {
        return convertToCommentVO(comment, userMap, repliesMap, null);
    }

    /**
     * 将评论对象转换为评论VO对象（支持用户相关信息）
     */
    private MomentCommentVO convertToCommentVO(MomentComment comment, Map<Long, User> userMap, Map<Long, List<MomentComment>> repliesMap, Long currentUserId) {
        MomentCommentVO vo = new MomentCommentVO();
        BeanUtils.copyProperties(comment, vo);
        
        // 设置用户信息
        User user = userMap.get(comment.getUserId());
        if (user != null) {
            vo.setUserName(user.getName());
            vo.setUserAvatar(user.getAvatarUrl());
        }

        // 设置投票信息（暂时设为默认值）
        vo.setUpVotes(0);
        vo.setDownVotes(0);
        vo.setUpVoted(false);
        vo.setDownVoted(false);
        
        // 设置回复列表
        List<MomentComment> replies = repliesMap.getOrDefault(comment.getId(), new ArrayList<>());
        if (!replies.isEmpty()) {
            List<MomentCommentVO> replyVOs = new ArrayList<>();
            for (MomentComment reply : replies) {
                MomentCommentVO replyVO = new MomentCommentVO();
                BeanUtils.copyProperties(reply, replyVO);
                
                // 设置回复的用户信息
                User replyUser = userMap.get(reply.getUserId());
                if (replyUser != null) {
                    replyVO.setUserName(replyUser.getName());
                    replyVO.setUserAvatar(replyUser.getAvatarUrl());
                }

                // 设置回复的投票信息（暂时设为默认值）
                replyVO.setUpVotes(0);
                replyVO.setDownVotes(0);
                replyVO.setUpVoted(false);
                replyVO.setDownVoted(false);

                replyVOs.add(replyVO);
            }
            vo.setReplies(replyVOs);
        }
        
        return vo;
    }
} 