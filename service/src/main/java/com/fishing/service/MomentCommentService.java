package com.fishing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.moment.MomentComment;
import com.fishing.vo.moment.MomentCommentVO;

import java.util.List;

/**
 * 动态评论服务接口
 */
public interface MomentCommentService extends IService<MomentComment> {
    
    /**
     * 添加评论
     * 
     * @param comment 评论信息
     * @return 评论ID
     */
    Long addComment(MomentComment comment);
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteComment(Long commentId, Long userId);
    
    /**
     * 获取动态评论列表
     *
     * @param momentId 动态ID
     * @param page 分页参数
     * @return 评论列表
     */
    Page<MomentCommentVO> getCommentsByMomentId(Long momentId, Page<MomentComment> page);

    /**
     * 获取动态评论列表（支持用户相关信息）
     *
     * @param momentId 动态ID
     * @param page 分页参数
     * @param currentUserId 当前用户ID（可为空）
     * @return 评论列表
     */
    Page<MomentCommentVO> getCommentsByMomentId(Long momentId, Page<MomentComment> page, Long currentUserId);

    /**
     * 获取评论的回复列表
     *
     * @param commentId 评论ID
     * @param page 分页参数
     * @return 回复列表
     */
    Page<MomentCommentVO> getRepliesByCommentId(Long commentId, Page<MomentComment> page);

    /**
     * 获取评论的回复列表（支持用户相关信息）
     *
     * @param commentId 评论ID
     * @param page 分页参数
     * @param currentUserId 当前用户ID（可为空）
     * @return 回复列表
     */
    Page<MomentCommentVO> getRepliesByCommentId(Long commentId, Page<MomentComment> page, Long currentUserId);
    
    /**
     * 获取评论数量
     * 
     * @param momentId 动态ID
     * @return 评论数量
     */
    Integer getCommentCount(Long momentId);
    
    /**
     * 批量获取动态评论数
     * 
     * @param momentIds 动态ID列表
     * @return 动态ID到评论数的映射
     */
    List<Long> batchGetCommentCounts(List<Long> momentIds);
} 